# Test script to validate app.R syntax
# This script checks if the app.R file can be parsed without errors

cat("Testing app.R syntax...\n")

# Try to parse the app.R file
tryCatch({
    parse("app.R")
    cat("✓ app.R syntax is valid\n")
}, error = function(e) {
    cat("✗ Syntax error in app.R:\n")
    cat(e$message, "\n")
})

# Check if required libraries can be loaded (if available)
required_libs <- c("shiny", "shinydashboard", "visNetwork", "dplyr", "DT")

cat("\nChecking required libraries:\n")
for (lib in required_libs) {
    if (requireNamespace(lib, quietly = TRUE)) {
        cat("✓", lib, "is available\n")
    } else {
        cat("✗", lib, "is not installed\n")
    }
}

cat("\nSyntax validation complete.\n")
